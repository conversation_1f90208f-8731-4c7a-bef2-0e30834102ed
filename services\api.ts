// 我们可以从 Web 项目中复制这个类型定义
export type Note = {
  id: number;
  title: string;
  content: string;
};

// !!! 重要 !!!
// 这里的 IP 地址需要换成你电脑的局域网 IP 地址
// 因为手机和电脑在同一个 Wi-Fi 下，手机需要通过 IP 地址访问电脑上运行的 Next.js 服务
// 在终端(macOS/Linux: ifconfig, Windows: ipconfig)中找到你的 IPv4 地址
const API_BASE_URL = 'http://**************:3000/api'; // <--- 替换成你的 IP 地址!

export const getNotes = async (): Promise<Note[]> => {
  const res = await fetch(`${API_BASE_URL}/notes`);
  if (!res.ok) {
    throw new Error('Failed to fetch notes');
  }
  return res.json();
};

export const getNoteById = async (id: number): Promise<Note> => {
  const res = await fetch(`${API_BASE_URL}/notes/${id}`);
  if (!res.ok) {
    throw new Error('Failed to fetch note');
  }
  return res.json();
};